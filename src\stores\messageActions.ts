
import { GoogleGenerativeAI, GenerationConfig } from '@google/generative-ai';
import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import { AppState, Message, ChecklistItem } from './types';
import { ConversationActions } from './conversationActions';
import configService from '../services/configService';

export interface MessageActions {
  togglePinMessage: (panelType: 'core' | 'plan', messageId: string) => void;
  sendMessage: (panelType: 'core' | 'plan', content: string) => void;
  sendGeneralChatMessage: (content: string) => void;
  clearGeneralChat: () => void;
  refreshCompass: () => void;
  addAssistantMessage: (panelType: 'core' | 'plan', content: string) => void;
  updateAssistantMessage: (panelType: 'core' | 'plan' | 'general', messageId: string, content: string) => void;
  setLoading: (panel: 'core' | 'plan' | 'compass' | 'general', loading: boolean) => void;
  deleteMessage: (panelType: 'core' | 'plan' | 'general', messageId: string) => void;
  editMessage: (panelType: 'core' | 'plan' | 'general', messageId: string, newContent: string) => void;
  refreshAssistantMessage: (panelType: 'core' | 'plan' | 'general', messageId: string) => void;
  resendUserMessage: (panelType: 'core' | 'plan' | 'general', messageId: string) => void;
}

export const createMessageActions = (
  set: (partial: Partial<AppState> | ((state: AppState) => Partial<AppState>)) => void,
  get: () => AppState,
  conversationActions: ConversationActions
): MessageActions => {
  // Helper functions for saving data to localStorage
  const saveConversationsToStorage = (conversations: AppState['conversations']) => {
    try {
      configService.saveConversations(conversations);
    } catch (error) {
      console.error('Failed to save conversations to storage:', error);
    }
  };

  const saveGeneralChatToStorage = (messages: AppState['generalChatMessages']) => {
    try {
      configService.saveGeneralChat(messages);
    } catch (error) {
      console.error('Failed to save general chat to storage:', error);
    }
  };

  const saveCompassDataToStorage = (summary: string, checklist: AppState['compassChecklist']) => {
    try {
      configService.saveCompassData(summary, checklist);
    } catch (error) {
      console.error('Failed to save compass data to storage:', error);
    }
  };
  const updateAssistantMessage = (panelType: 'core' | 'plan' | 'general', messageId: string, content: string) => {
    if (panelType === 'general') {
      set((state) => {
        const updatedMessages = state.generalChatMessages.map(msg =>
          msg.id === messageId ? { ...msg, content } : msg
        );
        saveGeneralChatToStorage(updatedMessages);
        return { generalChatMessages: updatedMessages };
      });
    } else {
      const activeConversation = conversationActions.getActiveConversation();
      if (!activeConversation) return;

      set((state) => {
        const updatedConversations = state.conversations.map(conv =>
          conv.id === activeConversation.id
            ? {
                ...conv,
                messages: {
                  ...conv.messages,
                  [panelType]: conv.messages[panelType].map(msg =>
                    msg.id === messageId ? { ...msg, content } : msg
                  )
                }
              }
            : conv
        );
        saveConversationsToStorage(updatedConversations);
        return { conversations: updatedConversations };
      });
    }
  };

  const refreshCompass = async () => {
    set((state) => ({ isLoading: { ...state.isLoading, compass: true } }));

    const visibleMessages = conversationActions.getVisibleMessages(); // Only use non-shielded conversations
    const totalMessages = visibleMessages.length;

    if (totalMessages === 0) {
      set((state) => ({
        compassSummary: '# Welcome to SynergyAI\n\nStart a conversation to see the AI-generated summary and progress tracking here.',
        compassChecklist: [],
        isLoading: { ...state.isLoading, compass: false }
      }));
      return;
    }

    const { modelSettings, apiKeys } = get();
    const compassSettings = modelSettings.compass;
    const model = compassSettings.model;

    // Prepare conversation context for analysis
    const conversationContext = visibleMessages.slice(-20).map(msg =>
      `${msg.role}: ${msg.content}`
    ).join('\n\n');

    const analysisPrompt = `Analyze the following conversation and provide:
1. A comprehensive summary in markdown format
2. Key themes and insights
3. Progress assessment
4. Strategic recommendations

Conversation context:
${conversationContext}

Please format your response as a detailed markdown analysis focusing on the user's goals, progress, and next steps.`;

    try {
      let analysisResult = '';

      // OpenRouter API integration for compass
      if (apiKeys.openrouter && (model.includes('/') || model.startsWith('deepseek'))) {
        const openai = new OpenAI({
          baseURL: 'https://openrouter.ai/api/v1',
          apiKey: apiKeys.openrouter,
        });

        const response = await openai.chat.completions.create({
          model: model,
          messages: [{ role: 'user', content: analysisPrompt }],
          temperature: compassSettings.temperature,
          max_tokens: compassSettings.maxOutputTokens,
        });

        analysisResult = response.choices[0]?.message?.content || 'Analysis could not be generated.';
      }
      // Gemini API integration for compass
      else if (model.startsWith('gemini') && apiKeys.google) {
        const genAI = new GoogleGenerativeAI(apiKeys.google);
        const generationConfig: GenerationConfig = {
          temperature: compassSettings.temperature,
          ...(compassSettings.hasOwnProperty('thinkingBudget') && typeof compassSettings.thinkingBudget === 'number'
            ? { maxOutputTokens: compassSettings.thinkingBudget }
            : compassSettings.maxOutputTokens
              ? { maxOutputTokens: compassSettings.maxOutputTokens }
              : {})
        };

        const geminiModel = genAI.getGenerativeModel({ model, generationConfig });
        const result = await geminiModel.generateContent(analysisPrompt);
        const response = await result.response;
        analysisResult = response.text();
      }
      // OpenAI API integration for compass
      else if (model.startsWith('gpt') && apiKeys.openai) {
        const openai = new OpenAI({
          apiKey: apiKeys.openai,
        });

        const response = await openai.chat.completions.create({
          model: model,
          messages: [{ role: 'user', content: analysisPrompt }],
          temperature: compassSettings.temperature,
          max_tokens: compassSettings.maxOutputTokens,
        });

        analysisResult = response.choices[0]?.message?.content || 'Analysis could not be generated.';
      }
      // Anthropic API integration for compass
      else if (model.startsWith('claude') && apiKeys.anthropic) {
        const anthropic = new Anthropic({
          apiKey: apiKeys.anthropic,
        });

        const response = await anthropic.messages.create({
          model: model,
          max_tokens: compassSettings.maxOutputTokens,
          temperature: compassSettings.temperature,
          messages: [{ role: 'user', content: analysisPrompt }],
        });

        analysisResult = response.content[0]?.type === 'text' ? response.content[0].text : 'Analysis could not be generated.';
      }
      else {
        analysisResult = `# Configuration Required

No API key found for the selected compass model: **${model}**

Please configure the appropriate API key in settings to enable AI-powered compass analysis.

## Current Conversation Stats
- **${totalMessages} messages** in visible conversations
- Analysis requires API key for model: ${model}

*Configure your API keys in Settings to unlock AI-powered insights.*`;
      }

      // Generate simple checklist based on conversation progress
      const checklist: ChecklistItem[] = [
        { id: '1', task: 'Define core objectives', isComplete: totalMessages > 2 },
        { id: '2', task: 'Gather requirements', isComplete: totalMessages > 4 },
        { id: '3', task: 'Analyze potential solutions', isComplete: totalMessages > 6 },
        { id: '4', task: 'Validate approach', isComplete: totalMessages > 10 },
        { id: '5', task: 'Implement solution', isComplete: false },
      ];

      set((state) => ({
        compassSummary: analysisResult,
        compassChecklist: checklist,
        isLoading: { ...state.isLoading, compass: false }
      }));

    } catch (error) {
      console.error('Compass analysis error:', error);
      set((state) => ({
        compassSummary: `# Analysis Error

An error occurred while generating the compass analysis: ${error instanceof Error ? error.message : 'Unknown error'}

Please check your API configuration and try again.`,
        compassChecklist: [],
        isLoading: { ...state.isLoading, compass: false }
      }));
    }
  };

  return {
    togglePinMessage: (panelType, messageId) => {
      const activeConversation = conversationActions.getActiveConversation();
      if (!activeConversation) return;

      set((state) => ({
        conversations: state.conversations.map(conv =>
          conv.id === activeConversation.id
            ? {
                ...conv,
                messages: {
                  ...conv.messages,
                  [panelType]: conv.messages[panelType].map(msg =>
                    msg.id === messageId ? { ...msg, isPinned: !msg.isPinned } : msg
                  )
                }
              }
            : conv
        )
      }));
    },

    sendMessage: async (panelType, content) => {
      const activeConversation = conversationActions.getActiveConversation();
      if (!activeConversation) return;

      // Add user message
      const userMessage: Message = {
        id: Date.now().toString() + '_user',
        role: 'user',
        content,
        isPinned: false,
        timestamp: new Date(),
      };

      set((state) => {
        const updatedConversations = state.conversations.map(conv =>
          conv.id === activeConversation.id
            ? {
                ...conv,
                messages: {
                  ...conv.messages,
                  [panelType]: [...conv.messages[panelType], userMessage]
                }
              }
            : conv
        );
        saveConversationsToStorage(updatedConversations);
        return {
          conversations: updatedConversations,
          isLoading: { ...state.isLoading, [panelType]: true }
        };
      });

      // Create assistant message
      const assistantMessage: Message = {
        id: Date.now().toString() + '_assistant',
        role: 'assistant',
        content: '',
        isPinned: false,
        timestamp: new Date(),
      };

      set((state) => {
        const updatedConversations = state.conversations.map(conv =>
          conv.id === activeConversation.id
            ? {
                ...conv,
                messages: {
                  ...conv.messages,
                  [panelType]: [...conv.messages[panelType], assistantMessage]
                }
              }
            : conv
        );
        saveConversationsToStorage(updatedConversations);
        return { conversations: updatedConversations };
      });

      const { modelSettings, apiKeys } = get();
      const panelSettings = modelSettings[panelType];
      const model = panelSettings.model;

      // OpenRouter API integration
      if (apiKeys.openrouter && (model.includes('/') || model.startsWith('deepseek'))) {
        try {
          const openai = new OpenAI({
            baseURL: 'https://openrouter.ai/api/v1',
            apiKey: apiKeys.openrouter,
          });

          const isReasoningModel = model.includes('deepseek') || model.includes('r1');
          
          const completionParams: any = {
            model: model,
            messages: [
              {
                role: 'user',
                content: content,
              },
            ],
            temperature: panelSettings.temperature,
            max_tokens: panelSettings.maxOutputTokens,
          };

          // Add reasoning configuration for models that support it
          if (isReasoningModel) {
            completionParams.reasoning = {
              effort: 'high',
              exclude: false, // Include reasoning in response for debugging
            };
          }

          const response = await openai.chat.completions.create(completionParams);
          
          let responseContent = response.choices[0]?.message?.content || 'No response received';
          
          // Log reasoning if available using type assertion
          const messageWithReasoning = response.choices[0]?.message as any;
          if (messageWithReasoning?.reasoning) {
            console.log('OpenRouter Reasoning:', messageWithReasoning.reasoning);
          }

          updateAssistantMessage(panelType, assistantMessage.id, responseContent);

        } catch (err) {
          console.error('OpenRouter API Error:', err);
          let errorMessage = 'An error occurred while communicating with the OpenRouter API.';
          if (err instanceof Error) {
            if (err.message.includes('API key')) {
              errorMessage = 'Error: Invalid OpenRouter API Key. Please check your settings.';
            } else if (err.message.includes('model')) {
              errorMessage = 'Error: Model not available or invalid. Please check the model name.';
            } else {
              errorMessage = err.message;
            }
          }
          updateAssistantMessage(panelType, assistantMessage.id, errorMessage);
        } finally {
          set((state) => ({ isLoading: { ...state.isLoading, [panelType]: false } }));
          refreshCompass();
        }
        return;
      }

      // Gemini API block (with "thinking budget" logic properly placed)
      if (model.startsWith('gemini')) {
        if (!apiKeys.google) {
          updateAssistantMessage(panelType, assistantMessage.id, "Error: Google API Key not provided. Please add it in the settings.");
          set((state) => ({ isLoading: { ...state.isLoading, [panelType]: false } }));
          return;
        }
        const genAI = new GoogleGenerativeAI(apiKeys.google);
        // If there is a 'thinkingBudget' setting, include it as maxTokens = thinkingBudget
        const generationConfig: GenerationConfig = {
          temperature: panelSettings.temperature,
          ...(panelSettings.hasOwnProperty('thinkingBudget') && typeof panelSettings.thinkingBudget === 'number'
            ? { maxOutputTokens: panelSettings.thinkingBudget }
            : panelSettings.maxOutputTokens
              ? { maxOutputTokens: panelSettings.maxOutputTokens }
              : {})
        };

        try {
          const geminiModel = genAI.getGenerativeModel({ model, generationConfig });
          const result = await geminiModel.generateContent(content);
          const response = await result.response;
          const text = response.text();

          updateAssistantMessage(panelType, assistantMessage.id, text);

        } catch (err) {
          console.error('Gemini API Error:', err);
          let errorMessage = 'An error occurred while communicating with the Gemini API.';
          if (err instanceof Error) {
            if (err.message.includes('API key not valid')) {
              errorMessage = 'Error: Invalid Google API Key. Please check your settings.';
            } else if (err.message.includes('permission')) {
              errorMessage = 'Error: API key does not have permission for this model. Please check your Google Cloud project.';
            } else {
              errorMessage = err.message;
            }
          }
          updateAssistantMessage(panelType, assistantMessage.id, errorMessage);
        } finally {
          set((state) => ({ isLoading: { ...state.isLoading, [panelType]: false } }));
          refreshCompass();
        }
        return;
      }

      // OpenAI API integration
      if (model.startsWith('gpt')) {
        if (!apiKeys.openai) {
          updateAssistantMessage(panelType, assistantMessage.id, "Error: OpenAI API Key not provided. Please add it in the settings.");
          set((state) => ({ isLoading: { ...state.isLoading, [panelType]: false } }));
          return;
        }

        try {
          const openai = new OpenAI({
            apiKey: apiKeys.openai,
          });

          // Build conversation history for better context
          const conversationHistory = activeConversation.messages[panelType].map(msg => ({
            role: msg.role as 'user' | 'assistant',
            content: msg.content
          }));

          // Add current message
          conversationHistory.push({
            role: 'user',
            content: content
          });

          const response = await openai.chat.completions.create({
            model: model,
            messages: conversationHistory,
            temperature: panelSettings.temperature,
            max_tokens: panelSettings.maxOutputTokens,
          });

          const responseContent = response.choices[0]?.message?.content || 'No response received';
          updateAssistantMessage(panelType, assistantMessage.id, responseContent);

        } catch (err) {
          console.error('OpenAI API Error:', err);
          let errorMessage = 'An error occurred while communicating with the OpenAI API.';
          if (err instanceof Error) {
            if (err.message.includes('API key')) {
              errorMessage = 'Error: Invalid OpenAI API Key. Please check your settings.';
            } else if (err.message.includes('model')) {
              errorMessage = 'Error: Model not available or invalid. Please check the model name.';
            } else if (err.message.includes('quota')) {
              errorMessage = 'Error: OpenAI API quota exceeded. Please check your billing.';
            } else {
              errorMessage = err.message;
            }
          }
          updateAssistantMessage(panelType, assistantMessage.id, errorMessage);
        } finally {
          set((state) => ({ isLoading: { ...state.isLoading, [panelType]: false } }));
          refreshCompass();
        }
        return;
      }

      // Anthropic API integration
      if (model.startsWith('claude')) {
        if (!apiKeys.anthropic) {
          updateAssistantMessage(panelType, assistantMessage.id, "Error: Anthropic API Key not provided. Please add it in the settings.");
          set((state) => ({ isLoading: { ...state.isLoading, [panelType]: false } }));
          return;
        }

        try {
          const anthropic = new Anthropic({
            apiKey: apiKeys.anthropic,
          });

          // Build conversation history for Anthropic
          const conversationHistory = activeConversation.messages[panelType];
          const messages: Array<{role: 'user' | 'assistant', content: string}> = [];

          // Add existing conversation history
          conversationHistory.forEach(msg => {
            messages.push({
              role: msg.role as 'user' | 'assistant',
              content: msg.content
            });
          });

          // Add current message
          messages.push({
            role: 'user',
            content: content
          });

          const response = await anthropic.messages.create({
            model: model,
            max_tokens: panelSettings.maxOutputTokens,
            temperature: panelSettings.temperature,
            messages: messages,
          });

          const responseContent = response.content[0]?.type === 'text' ? response.content[0].text : 'No response received';
          updateAssistantMessage(panelType, assistantMessage.id, responseContent);

        } catch (err) {
          console.error('Anthropic API Error:', err);
          let errorMessage = 'An error occurred while communicating with the Anthropic API.';
          if (err instanceof Error) {
            if (err.message.includes('API key')) {
              errorMessage = 'Error: Invalid Anthropic API Key. Please check your settings.';
            } else if (err.message.includes('model')) {
              errorMessage = 'Error: Model not available or invalid. Please check the model name.';
            } else if (err.message.includes('credit')) {
              errorMessage = 'Error: Anthropic API credits exhausted. Please check your billing.';
            } else {
              errorMessage = err.message;
            }
          }
          updateAssistantMessage(panelType, assistantMessage.id, errorMessage);
        } finally {
          set((state) => ({ isLoading: { ...state.isLoading, [panelType]: false } }));
          refreshCompass();
        }
        return;
      }

      // If no API key is provided for the selected model, show error
      updateAssistantMessage(panelType, assistantMessage.id, `Error: No API key configured for model "${model}". Please add the appropriate API key in settings.`);
      set((state) => ({ isLoading: { ...state.isLoading, [panelType]: false } }));
    },

    sendGeneralChatMessage: async (content) => {
      // Add user message
      const userMessage: Message = {
        id: Date.now().toString() + '_user_general',
        role: 'user',
        content,
        isPinned: false,
        timestamp: new Date(),
      };

      set((state) => {
        const updatedMessages = [...state.generalChatMessages, userMessage];
        saveGeneralChatToStorage(updatedMessages);
        return {
          generalChatMessages: updatedMessages,
          isLoading: { ...state.isLoading, general: true }
        };
      });

      // Create assistant message
      const assistantMessage: Message = {
        id: Date.now().toString() + '_assistant_general',
        role: 'assistant',
        content: '',
        isPinned: false,
        timestamp: new Date(),
      };
      
      set((state) => {
        const updatedMessages = [...state.generalChatMessages, assistantMessage];
        saveGeneralChatToStorage(updatedMessages);
        return { generalChatMessages: updatedMessages };
      });

      const { modelSettings, apiKeys } = get();
      const panelSettings = modelSettings.general;
      const model = panelSettings.model;

      // OpenRouter API integration for general chat
      if (apiKeys.openrouter && (model.includes('/') || model.startsWith('deepseek'))) {
        try {
          const openai = new OpenAI({
            baseURL: 'https://openrouter.ai/api/v1',
            apiKey: apiKeys.openrouter,
          });

          const isReasoningModel = model.includes('deepseek') || model.includes('r1');
          
          const completionParams: any = {
            model: model,
            messages: [
              {
                role: 'user',
                content: content,
              },
            ],
            temperature: panelSettings.temperature,
            max_tokens: panelSettings.maxOutputTokens,
          };

          if (isReasoningModel) {
            completionParams.reasoning = {
              effort: 'high',
              exclude: false,
            };
          }

          const response = await openai.chat.completions.create(completionParams);
          
          let responseContent = response.choices[0]?.message?.content || 'No response received';
          
          // Log reasoning if available using type assertion
          const messageWithReasoning = response.choices[0]?.message as any;
          if (messageWithReasoning?.reasoning) {
            console.log('OpenRouter General Chat Reasoning:', messageWithReasoning.reasoning);
          }

          set((state) => ({
            generalChatMessages: state.generalChatMessages.map(msg =>
              msg.id === assistantMessage.id ? { ...msg, content: responseContent } : msg
            )
          }));

        } catch (err) {
          console.error('OpenRouter General Chat API Error:', err);
          let errorMessage = 'An error occurred while communicating with the OpenRouter API.';
          if (err instanceof Error) {
            if (err.message.includes('API key')) {
              errorMessage = 'Error: Invalid OpenRouter API Key. Please check your settings.';
            } else {
              errorMessage = err.message;
            }
          }
          set((state) => ({
            generalChatMessages: state.generalChatMessages.map(msg =>
              msg.id === assistantMessage.id ? { ...msg, content: errorMessage } : msg
            )
          }));
        } finally {
          set((state) => ({ isLoading: { ...state.isLoading, general: false } }));
        }
        return;
      }

      if (model.startsWith('gemini')) {
        if (!apiKeys.google) {
          set((state) => ({
              generalChatMessages: state.generalChatMessages.map(msg =>
                  msg.id === assistantMessage.id ? { ...msg, content: "Error: Google API Key not provided. Please add it in the settings." } : msg
              ),
              isLoading: { ...state.isLoading, general: false }
          }));
          return;
        }
        const genAI = new GoogleGenerativeAI(apiKeys.google);
        const generationConfig: GenerationConfig = {
          temperature: panelSettings.temperature,
          ...(panelSettings.maxOutputTokens > 0 && { maxOutputTokens: panelSettings.maxOutputTokens }),
        };

        try {
            const geminiModel = genAI.getGenerativeModel({ model, generationConfig });
            const result = await geminiModel.generateContent(content);
            const response = await result.response;
            const text = response.text();

            set((state) => ({
                generalChatMessages: state.generalChatMessages.map(msg =>
                    msg.id === assistantMessage.id ? { ...msg, content: text } : msg
                )
            }));

        } catch (err) {
            console.error('Gemini API Error:', err);
            let errorMessage = 'An error occurred while communicating with the Gemini API.';
            if (err instanceof Error) {
                if (err.message.includes('API key not valid')) {
                    errorMessage = 'Error: Invalid Google API Key. Please check your settings.';
                } else if (err.message.includes('permission')) {
                    errorMessage = 'Error: API key does not have permission for this model. Please check your Google Cloud project.';
                } else {
                    errorMessage = err.message;
                }
            }
            set((state) => ({
                generalChatMessages: state.generalChatMessages.map(msg =>
                    msg.id === assistantMessage.id ? { ...msg, content: errorMessage } : msg
                )
            }));
        } finally {
            set((state) => ({ isLoading: { ...state.isLoading, general: false } }));
        }
        return;
      }

      // OpenAI API integration for general chat
      if (model.startsWith('gpt')) {
        if (!apiKeys.openai) {
          set((state) => ({
            generalChatMessages: state.generalChatMessages.map(msg =>
              msg.id === assistantMessage.id ? { ...msg, content: "Error: OpenAI API Key not provided. Please add it in the settings." } : msg
            ),
            isLoading: { ...state.isLoading, general: false }
          }));
          return;
        }

        try {
          const openai = new OpenAI({
            apiKey: apiKeys.openai,
          });

          // Build conversation history for better context
          const conversationHistory = get().generalChatMessages.map(msg => ({
            role: msg.role as 'user' | 'assistant',
            content: msg.content
          }));

          // Add current message
          conversationHistory.push({
            role: 'user',
            content: content
          });

          const response = await openai.chat.completions.create({
            model: model,
            messages: conversationHistory,
            temperature: panelSettings.temperature,
            max_tokens: panelSettings.maxOutputTokens,
          });

          const responseContent = response.choices[0]?.message?.content || 'No response received';

          set((state) => ({
            generalChatMessages: state.generalChatMessages.map(msg =>
              msg.id === assistantMessage.id ? { ...msg, content: responseContent } : msg
            )
          }));

        } catch (err) {
          console.error('OpenAI General Chat API Error:', err);
          let errorMessage = 'An error occurred while communicating with the OpenAI API.';
          if (err instanceof Error) {
            if (err.message.includes('API key')) {
              errorMessage = 'Error: Invalid OpenAI API Key. Please check your settings.';
            } else if (err.message.includes('quota')) {
              errorMessage = 'Error: OpenAI API quota exceeded. Please check your billing.';
            } else {
              errorMessage = err.message;
            }
          }
          set((state) => ({
            generalChatMessages: state.generalChatMessages.map(msg =>
              msg.id === assistantMessage.id ? { ...msg, content: errorMessage } : msg
            )
          }));
        } finally {
          set((state) => ({ isLoading: { ...state.isLoading, general: false } }));
        }
        return;
      }

      // Anthropic API integration for general chat
      if (model.startsWith('claude')) {
        if (!apiKeys.anthropic) {
          set((state) => ({
            generalChatMessages: state.generalChatMessages.map(msg =>
              msg.id === assistantMessage.id ? { ...msg, content: "Error: Anthropic API Key not provided. Please add it in the settings." } : msg
            ),
            isLoading: { ...state.isLoading, general: false }
          }));
          return;
        }

        try {
          const anthropic = new Anthropic({
            apiKey: apiKeys.anthropic,
          });

          // Build conversation history for Anthropic
          const conversationHistory = get().generalChatMessages;
          const messages: Array<{role: 'user' | 'assistant', content: string}> = [];

          // Add existing conversation history
          conversationHistory.forEach(msg => {
            if (msg.content.trim()) { // Skip empty messages
              messages.push({
                role: msg.role as 'user' | 'assistant',
                content: msg.content
              });
            }
          });

          // Add current message
          messages.push({
            role: 'user',
            content: content
          });

          const response = await anthropic.messages.create({
            model: model,
            max_tokens: panelSettings.maxOutputTokens,
            temperature: panelSettings.temperature,
            messages: messages,
          });

          const responseContent = response.content[0]?.type === 'text' ? response.content[0].text : 'No response received';

          set((state) => ({
            generalChatMessages: state.generalChatMessages.map(msg =>
              msg.id === assistantMessage.id ? { ...msg, content: responseContent } : msg
            )
          }));

        } catch (err) {
          console.error('Anthropic General Chat API Error:', err);
          let errorMessage = 'An error occurred while communicating with the Anthropic API.';
          if (err instanceof Error) {
            if (err.message.includes('API key')) {
              errorMessage = 'Error: Invalid Anthropic API Key. Please check your settings.';
            } else if (err.message.includes('credit')) {
              errorMessage = 'Error: Anthropic API credits exhausted. Please check your billing.';
            } else {
              errorMessage = err.message;
            }
          }
          set((state) => ({
            generalChatMessages: state.generalChatMessages.map(msg =>
              msg.id === assistantMessage.id ? { ...msg, content: errorMessage } : msg
            )
          }));
        } finally {
          set((state) => ({ isLoading: { ...state.isLoading, general: false } }));
        }
        return;
      }

      // If no API key is provided for the selected model, show error
      set((state) => ({
        generalChatMessages: state.generalChatMessages.map(msg =>
          msg.id === assistantMessage.id ? { ...msg, content: `Error: No API key configured for model "${model}". Please add the appropriate API key in settings.` } : msg
        ),
        isLoading: { ...state.isLoading, general: false }
      }));
    },

    clearGeneralChat: () => {
      set({ generalChatMessages: [] });
      saveGeneralChatToStorage([]);
    },

    refreshCompass,

    addAssistantMessage: (panelType, content) => {
      const activeConversation = conversationActions.getActiveConversation();
      if (!activeConversation) return;

      const message: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content,
        isPinned: false,
        timestamp: new Date(),
      };

      set((state) => ({
        conversations: state.conversations.map(conv =>
          conv.id === activeConversation.id
            ? {
                ...conv,
                messages: {
                  ...conv.messages,
                  [panelType]: [...conv.messages[panelType], message]
                }
              }
            : conv
        )
      }));
    },

    updateAssistantMessage,

    setLoading: (panel, loading) => {
      set((state) => ({
        isLoading: { ...state.isLoading, [panel]: loading }
      }));
    },

    deleteMessage: (panelType, messageId) => {
      if (panelType === 'general') {
        set((state) => ({
          generalChatMessages: state.generalChatMessages.filter(msg => msg.id !== messageId)
        }));
      } else {
        const activeConversation = conversationActions.getActiveConversation();
        if (!activeConversation) return;

        set((state) => ({
          conversations: state.conversations.map(conv =>
            conv.id === activeConversation.id
              ? {
                  ...conv,
                  messages: {
                    ...conv.messages,
                    [panelType]: conv.messages[panelType].filter(msg => msg.id !== messageId)
                  }
                }
              : conv
          )
        }));
      }
    },

    editMessage: (panelType, messageId, newContent) => {
      if (panelType === 'general') {
        set((state) => ({
          generalChatMessages: state.generalChatMessages.map(msg =>
            msg.id === messageId ? { ...msg, content: newContent } : msg
          )
        }));
      } else {
        const activeConversation = conversationActions.getActiveConversation();
        if (!activeConversation) return;

        set((state) => ({
          conversations: state.conversations.map(conv =>
            conv.id === activeConversation.id
              ? {
                  ...conv,
                  messages: {
                    ...conv.messages,
                    [panelType]: conv.messages[panelType].map(msg =>
                      msg.id === messageId ? { ...msg, content: newContent } : msg
                    )
                  }
                }
              : conv
          )
        }));
      }
    },

    refreshAssistantMessage: async (panelType, messageId) => {
      // Find the message and regenerate response
      let targetMessage: Message | undefined;
      
      if (panelType === 'general') {
        targetMessage = get().generalChatMessages.find(msg => msg.id === messageId);
      } else {
        const activeConversation = conversationActions.getActiveConversation();
        if (activeConversation) {
          targetMessage = activeConversation.messages[panelType].find(msg => msg.id === messageId);
        }
      }

      if (!targetMessage || targetMessage.role !== 'assistant') return;

      // Set loading state
      set((state) => ({
        isLoading: { ...state.isLoading, [panelType]: true }
      }));

      // Simulate regenerating response
      const responses = [
        "Let me provide you with a fresh perspective on this...",
        "Here's an updated analysis of your request:",
        "After reconsidering, I think we should approach this differently:",
        "\n\nThis refreshed response aims to provide better insights."
      ];

      // Clear the message content first
      if (panelType === 'general') {
        set((state) => ({
          generalChatMessages: state.generalChatMessages.map(msg =>
            msg.id === messageId ? { ...msg, content: '' } : msg
          )
        }));
      } else {
        const activeConversation = conversationActions.getActiveConversation();
        if (activeConversation) {
          set((state) => ({
            conversations: state.conversations.map(conv =>
              conv.id === activeConversation.id
                ? {
                    ...conv,
                    messages: {
                      ...conv.messages,
                      [panelType]: conv.messages[panelType].map(msg =>
                        msg.id === messageId ? { ...msg, content: '' } : msg
                      )
                    }
                  }
                : conv
            )
          }));
        }
      }

      // Stream the new response
      for (let i = 0; i < responses.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 400));
        const partialContent = responses.slice(0, i + 1).join('\n');
        
        if (panelType === 'general') {
          set((state) => ({
            generalChatMessages: state.generalChatMessages.map(msg =>
              msg.id === messageId ? { ...msg, content: partialContent } : msg
            )
          }));
        } else {
          const activeConversation = conversationActions.getActiveConversation();
          if (activeConversation) {
            set((state) => ({
              conversations: state.conversations.map(conv =>
                conv.id === activeConversation.id
                  ? {
                      ...conv,
                      messages: {
                        ...conv.messages,
                        [panelType]: conv.messages[panelType].map(msg =>
                          msg.id === messageId ? { ...msg, content: partialContent } : msg
                        )
                      }
                    }
                  : conv
              )
            }));
          }
        }
      }

      set((state) => ({
        isLoading: { ...state.isLoading, [panelType]: false }
      }));
    },

    resendUserMessage: async (panelType, messageId) => {
      // Find the user message and resend it
      let targetMessage: Message | undefined;
      
      if (panelType === 'general') {
        targetMessage = get().generalChatMessages.find(msg => msg.id === messageId);
        if (targetMessage && targetMessage.role === 'user') {
          // Create a reference to the sendGeneralChatMessage function
          const actions = get() as any;
          if (actions.sendGeneralChatMessage) {
            actions.sendGeneralChatMessage(targetMessage.content);
          }
        }
      } else {
        const activeConversation = conversationActions.getActiveConversation();
        if (activeConversation) {
          targetMessage = activeConversation.messages[panelType].find(msg => msg.id === messageId);
          if (targetMessage && targetMessage.role === 'user') {
            // Create a reference to the sendMessage function  
            const actions = get() as any;
            if (actions.sendMessage) {
              actions.sendMessage(panelType, targetMessage.content);
            }
          }
        }
      }
    },
  };
};
