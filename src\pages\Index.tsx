
import React, { useState } from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { Cog, Moon, Sun, ArrowLeft, Menu, Users, BarChart3 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import useAppStore from '../stores/useAppStore';
import { useTheme } from '../contexts/ThemeContext';
import ConversationSidebar from '../components/ConversationSidebar';
import UnifiedChatPanel from '../components/UnifiedChatPanel';
import CompassPanel from '../components/CompassPanel';
import SettingsModal from '../components/SettingsModal';

const Index: React.FC = () => {
  const navigate = useNavigate();
  const { toggleSettingsModal } = useAppStore();
  const { theme, toggleTheme } = useTheme();
  
  // State for managing panel visibility on mobile
  const [showConversations, setShowConversations] = useState(false);
  const [showCompass, setShowCompass] = useState(false);

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Header */}
      <header className="bg-card border-b border-border px-4 md:px-8 py-4 md:py-6 shadow-soft">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 md:gap-6">
            <button
              onClick={() => navigate('/')}
              className="p-2 md:p-3 text-muted-foreground hover:text-foreground hover:bg-accent rounded-xl transition-all duration-200"
              title="Back to Workspaces"
            >
              <ArrowLeft className="w-4 h-4 md:w-5 md:h-5" />
            </button>
            <div>
              <h1 className="text-xl md:text-3xl font-semibold text-foreground">SynergyAI</h1>
              <p className="text-muted-foreground text-sm md:text-base mt-1 md:mt-2 font-normal hidden sm:block">
                Multi-agent AI workspace for accelerated problem-solving
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2 md:gap-3">
            {/* Mobile panel toggles */}
            <div className="flex items-center gap-1 md:hidden">
              <button
                onClick={() => setShowConversations(!showConversations)}
                className={`p-2 rounded-xl transition-all duration-200 ${
                  showConversations 
                    ? 'bg-accent text-foreground' 
                    : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                }`}
                title="Toggle conversations"
              >
                <Users className="w-4 h-4" />
              </button>
              <button
                onClick={() => setShowCompass(!showCompass)}
                className={`p-2 rounded-xl transition-all duration-200 ${
                  showCompass 
                    ? 'bg-accent text-foreground' 
                    : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                }`}
                title="Toggle Compass"
              >
                <BarChart3 className="w-4 h-4" />
              </button>
            </div>
            <button
              onClick={toggleTheme}
              className="p-2 md:p-3 text-muted-foreground hover:text-foreground hover:bg-accent rounded-xl transition-all duration-200"
              title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
            >
              {theme === 'light' ? <Moon className="w-4 h-4 md:w-5 md:h-5" /> : <Sun className="w-4 h-4 md:w-5 md:h-5" />}
            </button>
            <button
              onClick={toggleSettingsModal}
              className="p-2 md:p-3 text-muted-foreground hover:text-foreground hover:bg-accent rounded-xl transition-all duration-200"
              title="Settings"
            >
              <Cog className="w-4 h-4 md:w-5 md:h-5" />
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="h-[calc(100vh-80px)] md:h-[calc(100vh-100px)] p-3 md:p-6">
        {/* Mobile overlay panels */}
        <div className="md:hidden relative h-full">
          {/* Conversation Sidebar Overlay */}
          {showConversations && (
            <>
              <div 
                className="fixed inset-0 bg-black/50 z-40"
                onClick={() => setShowConversations(false)}
              />
              <div className="fixed left-0 top-[80px] bottom-0 w-80 bg-card border-r border-border z-50 transform transition-transform duration-300">
                <ConversationSidebar />
              </div>
            </>
          )}
          
          {/* Compass Panel Overlay */}
          {showCompass && (
            <>
              <div 
                className="fixed inset-0 bg-black/50 z-40"
                onClick={() => setShowCompass(false)}
              />
              <div className="fixed right-0 top-[80px] bottom-0 w-80 bg-card border-l border-border z-50 transform transition-transform duration-300">
                <CompassPanel />
              </div>
            </>
          )}
          
          {/* Chat Panel - Full width on mobile */}
          <div className="h-full">
            <UnifiedChatPanel />
          </div>
        </div>

        {/* Desktop layout with resizable panels */}
        <div className="hidden md:block h-full">
          <PanelGroup direction="horizontal" className="h-full">
            {/* Conversation Sidebar */}
            <Panel defaultSize={20} minSize={15} maxSize={30}>
              <ConversationSidebar />
            </Panel>

            <PanelResizeHandle className="w-2 bg-transparent hover:bg-accent/50 transition-colors rounded-full mx-1" />

            {/* Chat Panel */}
            <Panel defaultSize={50} minSize={30}>
              <UnifiedChatPanel />
            </Panel>

            <PanelResizeHandle className="w-2 bg-transparent hover:bg-accent/50 transition-colors rounded-full mx-1" />

            {/* Compass Panel */}
            <Panel defaultSize={30} minSize={25}>
              <CompassPanel />
            </Panel>
          </PanelGroup>
        </div>
      </main>

      {/* Settings Modal */}
      <SettingsModal />
    </div>
  );
};

export default Index;
