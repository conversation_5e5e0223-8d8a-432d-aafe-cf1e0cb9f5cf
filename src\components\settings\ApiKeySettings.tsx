
import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AppState } from '@/stores/types';

interface ApiKeySettingsProps {
  apiKeys: AppState['apiKeys'];
  updateApiKey: (service: 'openai' | 'anthropic' | 'google' | 'openrouter', key: string) => void;
}

const ApiKeySettings: React.FC<ApiKeySettingsProps> = ({ apiKeys, updateApiKey }) => {
  return (
    <div>
      <h3 className="text-lg font-semibold mb-4 text-blue-400">API Keys</h3>
      <div className="space-y-4">
        <div>
          <Label htmlFor="openai-key" className="text-sm font-medium text-gray-300">
            OpenAI API Key
          </Label>
          <Input
            id="openai-key"
            type="password"
            placeholder="sk-..."
            value={apiKeys.openai}
            onChange={(e) => updateApiKey('openai', e.target.value)}
            className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500 mt-1"
          />
        </div>
        
        <div>
          <Label htmlFor="anthropic-key" className="text-sm font-medium text-gray-300">
            Anthropic API Key
          </Label>
          <Input
            id="anthropic-key"
            type="password"
            placeholder="sk-ant-..."
            value={apiKeys.anthropic}
            onChange={(e) => updateApiKey('anthropic', e.target.value)}
            className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500 mt-1"
          />
        </div>
        
        <div>
          <Label htmlFor="google-key" className="text-sm font-medium text-gray-300">
            Google API Key
          </Label>
          <Input
            id="google-key"
            type="password"
            placeholder="AIza..."
            value={apiKeys.google}
            onChange={(e) => updateApiKey('google', e.target.value)}
            className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500 mt-1"
          />
        </div>

        <div>
          <Label htmlFor="openrouter-key" className="text-sm font-medium text-gray-300">
            OpenRouter API Key
          </Label>
          <Input
            id="openrouter-key"
            type="password"
            placeholder="sk-or-..."
            value={apiKeys.openrouter}
            onChange={(e) => updateApiKey('openrouter', e.target.value)}
            className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500 mt-1"
          />
        </div>
      </div>
    </div>
  );
};

export default ApiKeySettings;
