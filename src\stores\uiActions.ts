
import { AppState, ModelConfig } from './types';
import { MessageActions } from './messageActions';
import configService from '../services/configService';

export interface UIActions {
  toggleSettingsModal: () => void;
  toggleBranchModal: () => void;
  setBranchTitle: (title: string) => void;
  setDraftPrompt: (prompt: string) => void;
  submitPromptToCore: (prompt: string) => void;
  updateApiKey: (service: 'openai' | 'anthropic' | 'google' | 'openrouter', key: string) => void;
  updateModelParameter: (panel: 'core' | 'plan' | 'compass' | 'general', parameter: keyof ModelConfig, value: string | number) => void;
  loadConfigurationFromStorage: () => void;
  saveConfigurationToStorage: () => void;
  clearAllConfiguration: () => void;
  validateApiKey: (service: 'openai' | 'anthropic' | 'google' | 'openrouter', key: string) => boolean;
}

export const createUIActions = (
  set: (partial: Partial<AppState> | ((state: AppState) => Partial<AppState>)) => void,
  get: () => AppState,
  messageActions: MessageActions
): UIActions => ({
  toggleSettingsModal: () => set((state) => ({ isSettingsModalOpen: !state.isSettingsModalOpen })),

  toggleBranchModal: () => set((state) => ({ isBranchModalOpen: !state.isBranchModalOpen, branchTitle: '' })),

  setBranchTitle: (title) => set({ branchTitle: title }),

  setDraftPrompt: (prompt) => set({ draftPrompt: prompt }),

  submitPromptToCore: (prompt) => {
    messageActions.sendMessage('core', prompt);
    set({ draftPrompt: '' });
  },

  updateApiKey: (service, key) => {
    set((state) => ({
      apiKeys: { ...state.apiKeys, [service]: key }
    }));

    // Save to localStorage whenever API keys are updated
    const currentState = get();
    const updatedApiKeys = { ...currentState.apiKeys, [service]: key };
    try {
      configService.saveApiKeys(updatedApiKeys);
    } catch (error) {
      console.error('Failed to save API key to storage:', error);
    }
  },

  updateModelParameter: (panel, parameter, value) => {
    set((state) => ({
      modelSettings: {
        ...state.modelSettings,
        [panel]: {
          ...state.modelSettings[panel],
          [parameter]: value
        }
      }
    }));

    // Save to localStorage whenever model settings are updated
    const currentState = get();
    try {
      configService.saveModelSettings(currentState.modelSettings);
    } catch (error) {
      console.error('Failed to save model settings to storage:', error);
    }
  },

  loadConfigurationFromStorage: () => {
    try {
      // Load API keys from environment variables and localStorage
      const apiKeyConfig = configService.loadApiKeys();
      const apiKeys = {
        openai: apiKeyConfig.openai.value,
        anthropic: apiKeyConfig.anthropic.value,
        google: apiKeyConfig.google.value,
        openrouter: apiKeyConfig.openrouter.value,
      };

      // Load model settings
      const storedModelSettings = configService.loadModelSettings();

      // Load conversations
      const storedConversations = configService.loadConversations();

      // Load general chat
      const storedGeneralChat = configService.loadGeneralChat();

      // Load compass data
      const storedCompassData = configService.loadCompassData();

      // Load app settings
      const storedAppSettings = configService.loadAppSettings();

      set((state) => ({
        apiKeys,
        ...(storedModelSettings && { modelSettings: storedModelSettings }),
        ...(storedConversations && { conversations: storedConversations }),
        ...(storedGeneralChat && { generalChatMessages: storedGeneralChat }),
        ...(storedCompassData && {
          compassSummary: storedCompassData.summary,
          compassChecklist: storedCompassData.checklist
        }),
        ...(storedAppSettings && {
          activeConversationId: storedAppSettings.activeConversationId || state.activeConversationId,
          currentWorkspace: storedAppSettings.currentWorkspace || state.currentWorkspace,
        }),
      }));

    } catch (error) {
      console.error('Failed to load configuration from storage:', error);
    }
  },

  saveConfigurationToStorage: () => {
    const state = get();
    try {
      configService.saveApiKeys(state.apiKeys);
      configService.saveModelSettings(state.modelSettings);
      configService.saveConversations(state.conversations);
      configService.saveGeneralChat(state.generalChatMessages);
      configService.saveCompassData(state.compassSummary, state.compassChecklist);
      configService.saveAppSettings({
        activeConversationId: state.activeConversationId,
        currentWorkspace: state.currentWorkspace,
      });
    } catch (error) {
      console.error('Failed to save configuration to storage:', error);
      throw error;
    }
  },

  clearAllConfiguration: () => {
    try {
      configService.clearAllData();
      // Reset to default state
      set((state) => ({
        apiKeys: {
          openai: '',
          anthropic: '',
          google: '',
          openrouter: '',
        },
        modelSettings: {
          core: { model: 'gpt-4o', temperature: 0.7, maxOutputTokens: 2048 },
          plan: { model: 'claude-3-haiku-20240307', temperature: 0.8, maxOutputTokens: 2048 },
          compass: { model: 'gemini-1.5-flash', temperature: 0.5, maxOutputTokens: 4096, thinkingBudget: 8192 },
          general: { model: 'gpt-4o-mini', temperature: 0.7, maxOutputTokens: 2048 },
        },
        conversations: [state.conversations[0]], // Keep at least one conversation
        generalChatMessages: [],
        compassSummary: '# Welcome to SynergyAI\n\nStart a conversation to see the AI-generated summary and progress tracking here.',
        compassChecklist: [],
        currentWorkspace: null,
      }));
    } catch (error) {
      console.error('Failed to clear configuration:', error);
      throw error;
    }
  },

  validateApiKey: (service, key) => {
    return configService.validateApiKey(service, key);
  },
});
