
import { AppState, ModelConfig } from './types';
import { MessageActions } from './messageActions';

export interface UIActions {
  toggleSettingsModal: () => void;
  toggleBranchModal: () => void;
  setBranchTitle: (title: string) => void;
  setDraftPrompt: (prompt: string) => void;
  submitPromptToCore: (prompt: string) => void;
  updateApiKey: (service: 'openai' | 'anthropic' | 'google' | 'openrouter', key: string) => void;
  updateModelParameter: (panel: 'core' | 'plan' | 'compass' | 'general', parameter: keyof ModelConfig, value: string | number) => void;
}

export const createUIActions = (
  set: (partial: Partial<AppState> | ((state: AppState) => Partial<AppState>)) => void,
  get: () => AppState,
  messageActions: MessageActions
): UIActions => ({
  toggleSettingsModal: () => set((state) => ({ isSettingsModalOpen: !state.isSettingsModalOpen })),

  toggleBranchModal: () => set((state) => ({ isBranchModalOpen: !state.isBranchModalOpen, branchTitle: '' })),

  setBranchTitle: (title) => set({ branchTitle: title }),

  setDraftPrompt: (prompt) => set({ draftPrompt: prompt }),

  submitPromptToCore: (prompt) => {
    messageActions.sendMessage('core', prompt);
    set({ draftPrompt: '' });
  },

  updateApiKey: (service, key) => set((state) => ({
    apiKeys: { ...state.apiKeys, [service]: key }
  })),

  updateModelParameter: (panel, parameter, value) => set((state) => ({
    modelSettings: {
      ...state.modelSettings,
      [panel]: {
        ...state.modelSettings[panel],
        [parameter]: value
      }
    }
  })),
});
