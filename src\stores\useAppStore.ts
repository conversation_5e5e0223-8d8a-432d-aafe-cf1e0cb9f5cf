
import { create } from 'zustand';
import { AppState } from './types';
import { createDefaultConversation, createConversationActions, ConversationActions } from './conversationActions';
import { createMessageActions, MessageActions } from './messageActions';
import { createUIActions, UIActions } from './uiActions';

// Re-export types for backward compatibility
export type { Message, ChecklistItem, Conversation, ModelConfig } from './types';

type AppActions = ConversationActions & MessageActions & UIActions;

const useAppStore = create<AppState & AppActions>((set, get) => {
  // Create conversation actions first
  const conversationActions = createConversationActions(set, get);
  
  // Create message actions with conversation actions dependency
  const messageActions = createMessageActions(set, get, conversationActions);
  
  // Create UI actions with message actions dependency
  const uiActions = createUIActions(set, get, messageActions);

  return {
    // Initial state
    conversations: [createDefaultConversation()],
    activeConversationId: '',
    generalChatMessages: [],
    compassSummary: '# Welcome to SynergyAI\n\nStart a conversation to see the AI-generated summary and progress tracking here.',
    compassChecklist: [],
    isLoading: {
      core: false,
      plan: false,
      compass: false,
      general: false,
    },
    apiKeys: {
      openai: '',
      anthropic: '',
      google: '',
      openrouter: '',
    },
    modelSettings: {
      core: { model: 'gpt-4o', temperature: 0.7, maxOutputTokens: 2048 },
      plan: { model: 'claude-3-haiku-20240307', temperature: 0.8, maxOutputTokens: 2048 },
      compass: { model: 'gemini-1.5-flash', temperature: 0.5, maxOutputTokens: 4096, thinkingBudget: 8192 },
      general: { model: 'gpt-4o-mini', temperature: 0.7, maxOutputTokens: 2048 },
    },
    isSettingsModalOpen: false,
    isBranchModalOpen: false,
    branchTitle: '',
    currentWorkspace: null,
    draftPrompt: '',

    // Actions
    ...conversationActions,
    ...messageActions,
    ...uiActions,
  };
});

// Initialize the first conversation as active
const initialState = useAppStore.getState();
if (initialState.conversations.length > 0 && !initialState.activeConversationId) {
  useAppStore.setState({ activeConversationId: initialState.conversations[0].id });
}

export default useAppStore;
