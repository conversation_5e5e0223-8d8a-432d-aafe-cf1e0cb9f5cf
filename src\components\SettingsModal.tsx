
import React from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import useAppStore from '../stores/useAppStore';
import ApiKeySettings from './settings/ApiKeySettings';
import ModelSettingsPanel from './settings/ModelSettingsPanel';

const SettingsModal: React.FC = () => {
  const {
    isSettingsModalOpen,
    toggleSettingsModal,
    apiKeys,
    modelSettings,
    updateApiKey,
    updateModelParameter,
  } = useAppStore();

  const modelOptions = {
    openai: ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo'],
    anthropic: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
    google: ['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-pro'],
    openrouter: [
      'openai/gpt-4o',
      'openai/gpt-4o-mini',
      'anthropic/claude-3-opus',
      'anthropic/claude-3-sonnet',
      'anthropic/claude-3-haiku',
      'google/gemini-pro-1.5',
      'meta-llama/llama-3.1-405b-instruct',
      'meta-llama/llama-3.1-70b-instruct',
      'mistralai/mixtral-8x7b-instruct'
    ],
  };

  const allModels = [...modelOptions.openai, ...modelOptions.anthropic, ...modelOptions.google, ...modelOptions.openrouter];

  return (
    <Dialog open={isSettingsModalOpen} onOpenChange={toggleSettingsModal}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] bg-gray-900 text-white border-gray-700 p-0">
        <DialogHeader className="px-6 pt-6 pb-2">
          <DialogTitle className="text-xl font-semibold">Settings</DialogTitle>
        </DialogHeader>
        
        <ScrollArea className="max-h-[calc(90vh-8rem)] px-6 pb-6">
          <div className="space-y-6">
            <ApiKeySettings apiKeys={apiKeys} updateApiKey={updateApiKey} />

            <div>
              <h3 className="text-lg font-semibold mb-4 text-blue-400">Model Settings</h3>
              <div className="space-y-8">
                <ModelSettingsPanel
                  panelId="core"
                  panelName="Core Model"
                  settings={modelSettings.core}
                  allModels={allModels}
                  updateModelParameter={updateModelParameter}
                />
                <ModelSettingsPanel
                  panelId="plan"
                  panelName="Plan Model"
                  settings={modelSettings.plan}
                  allModels={allModels}
                  updateModelParameter={updateModelParameter}
                />
                <ModelSettingsPanel
                  panelId="compass"
                  panelName="Compass Model"
                  settings={modelSettings.compass}
                  allModels={allModels}
                  updateModelParameter={updateModelParameter}
                />
                <ModelSettingsPanel
                  panelId="general"
                  panelName="General Chat Model"
                  settings={modelSettings.general}
                  allModels={allModels}
                  updateModelParameter={updateModelParameter}
                />
              </div>
            </div>
            
            <div className="text-sm text-gray-400 bg-gray-800 p-3 rounded-lg">
              <p className="font-medium mb-1">💡 Note:</p>
              <p>This is a demo version. API integration is simulated for demonstration purposes. In a production environment, these settings would connect to real AI services including OpenRouter for access to multiple model providers.</p>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default SettingsModal;
